package com.ke.chat.tag.service.configuration;

import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.ke.ctt.oauth.client.core.OAuthClient;
import com.lianjia.infrastructure.api.signature.http.okhttp.OkHttpSignInterceptor;
import com.lianjia.infrastructure.api.signature.sign.Credential;
import com.lianjia.infrastructure.api.signature.sign.credential.StaticCredentialProvider;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.cbor.MappingJackson2CborHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 配置restTemplate
 *
 * <AUTHOR>
 */
@Configuration
public class RestTemplateConfiguration {

    @Value("${online.iam.accessKeyId}")
    private String iamAK;
    @Value("${online.iam.accessKeySecret}")
    private String iamSK;

    @Autowired
    private OpenApiClientHttpRequestInterceptor openApiClientHttpRequestInterceptor;

    @Autowired
    private LoggingRequestInterceptor loggingRequestInterceptor;

    /**
     * 配置restTemplate
     *
     * @return
     */
    @Bean
    @Primary
    public RestTemplate restTemplate(RestTemplateBuilder builder, OkHttpClient okHttpClient) {
        return getBaseRestTemplate(builder, okHttpClient);
    }

    @NotNull
    private RestTemplate getBaseRestTemplate(RestTemplateBuilder builder, OkHttpClient okHttpClient) {
        RestTemplate restTemplate = builder.build();
        List<HttpMessageConverter<?>> messageConverters = restTemplate.getMessageConverters();
        // 移除默认的 MappingJackson2HttpMessageConverter、cbor也要移除，企微用户信息返回的是这个
        messageConverters.removeIf(converter -> converter instanceof MappingJackson2HttpMessageConverter || converter instanceof MappingJackson2CborHttpMessageConverter);
        // 添加 FastJsonHttpMessageConverter
        FastJsonHttpMessageConverter fastJsonHttpMessageConverter = new FastJsonHttpMessageConverter();
        // 配置 FastJsonConfig
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setCharset(StandardCharsets.UTF_8);
        fastJsonHttpMessageConverter.setFastJsonConfig(fastJsonConfig);
        // 设置支持的 MediaType
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        supportedMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        supportedMediaTypes.add(new MediaType("application", "cbor"));
        fastJsonHttpMessageConverter.setSupportedMediaTypes(supportedMediaTypes);
        messageConverters.add(fastJsonHttpMessageConverter);

        List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
        interceptors.add(loggingRequestInterceptor);

        restTemplate.setInterceptors(interceptors);
        OkHttp3ClientHttpRequestFactory httpFactory = new OkHttp3ClientHttpRequestFactory(okHttpClient);
        restTemplate.setRequestFactory(new BufferingClientHttpRequestFactory(httpFactory));
        return restTemplate;
    }

    /**
     * 经过网关验证的restTemplate,会自动根据环境切换
     *
     * @param builder
     * @param oauthClient
     * @return
     */
    @Bean(name = "getWayRestTemplate")
    public RestTemplate getWayRestTemplate(RestTemplateBuilder builder, OAuthClient oauthClient, OkHttpClient okHttpClient) {
        RestTemplate restTemplate = getBaseRestTemplate(builder, okHttpClient);
        restTemplate.getInterceptors().add(new OAuthAccessTokenClientHttpRequestInterceptor(oauthClient));
        return restTemplate;
    }

    /**
     * 经过网关验证的restTemplate，直指线上，用户无法线下测试的场景，比如企微机器人，无特殊原因不要使用
     *
     * @param builder
     * @param oauthClient
     * @return
     */
    @Bean(name = "onlineGetWayRestTemplate")
    public RestTemplate onlineGetWayRestTemplate(RestTemplateBuilder builder, @Qualifier("olineAuthClient") OAuthClient oauthClient, OkHttpClient okHttpClient) {
        RestTemplate restTemplate = getBaseRestTemplate(builder, okHttpClient);
        restTemplate.getInterceptors().add(new OAuthAccessTokenClientHttpRequestInterceptor(oauthClient));
        return restTemplate;
    }


    @Bean(name = "openApiRestTemplate")
    public RestTemplate openApiRestTemplate(RestTemplateBuilder builder, OkHttpClient okHttpClient) {
        RestTemplate restTemplate = getBaseRestTemplate(builder, okHttpClient);
        restTemplate.getInterceptors().add(openApiClientHttpRequestInterceptor);
        restTemplate.getInterceptors().add(new OpenApiHttpExecuteExceptionHandlingInterceptor());
        return restTemplate;
    }

    @Bean(name = "bkQwApiRestTemplate")
    public RestTemplate bkQwApiRestTemplate(RestTemplateBuilder builder, OkHttpClient okHttpClient) {
        RestTemplate restTemplate = getBaseRestTemplate(builder, okHttpClient);
        restTemplate.getInterceptors().add(new BkQwApiHttpExecuteExceptionHandlingInterceptor());
        return restTemplate;
    }

    @Bean(name = "aiExamRestTemplate")
    public RestTemplate aiExamRestTemplate(RestTemplateBuilder builder, OkHttpClient okHttpClient) {
        RestTemplate restTemplate = getBaseRestTemplate(builder, okHttpClient);
        restTemplate.getInterceptors().add(new HttpStatusCheckRequestInterceptor());
        return restTemplate;
    }


    /**
     * 经过iam验证的RestTemplate online
     */
    @Bean(name = "iamOnlineRestTemplate")
    public RestTemplate getIamRestTemplate(RestTemplateBuilder builder, @Qualifier("iamOnlineOkHttpClient") OkHttpClient iamOkHttpClient) {
        return getBaseRestTemplate(builder, iamOkHttpClient);
    }

    /**
     * 通用的RestTemplate，用于一般的HTTP请求
     */
    @Bean(name = "addRestTemplate")
    public RestTemplate addRestTemplate(RestTemplateBuilder builder, OkHttpClient okHttpClient) {
        return getBaseRestTemplate(builder, okHttpClient);
    }


    @Bean
    @Primary
    public OkHttpClient okHttpClient() {
        Dispatcher dispatcher = new Dispatcher();
        // 设置全局最大并发请求数
        dispatcher.setMaxRequests(500);
        // 设置每个主机的最大并发请求数
        dispatcher.setMaxRequestsPerHost(50);

        // 配置超时、连接池
        return new OkHttpClient.Builder()
            .connectTimeout(120, TimeUnit.SECONDS)
            .readTimeout(120, TimeUnit.SECONDS)
            .writeTimeout(180, TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(50, 5, TimeUnit.MINUTES))
            .dispatcher(dispatcher)
            .build();
    }


    @Bean(name = "iamOnlineOkHttpClient")
    public OkHttpClient iamOkHttpClient() {
        Dispatcher dispatcher = new Dispatcher();
        // 设置全局最大并发请求数
        dispatcher.setMaxRequests(50);
        // 设置每个主机的最大并发请求数
        dispatcher.setMaxRequestsPerHost(5);

        // 配置超时、连接池
        return new OkHttpClient.Builder()
            .connectTimeout(120, TimeUnit.SECONDS)
            .addInterceptor((new OkHttpSignInterceptor()
                .setCredentialProvider(new StaticCredentialProvider(new Credential(iamAK, iamSK)))))
            .readTimeout(120, TimeUnit.SECONDS)
            .writeTimeout(180, TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(10, 5, TimeUnit.MINUTES))
            .dispatcher(dispatcher)
            .build();
    }
}
