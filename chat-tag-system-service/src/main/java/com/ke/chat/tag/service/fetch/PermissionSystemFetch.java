package com.ke.chat.tag.service.fetch;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Sets;
import com.ke.chat.tag.service.fetch.entity.permission.RolePermission;
import com.ke.chat.tag.service.fetch.entity.permission.UserRoleAndPermissionResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 权限系统接口
 * <AUTHOR>
 * @date 2024/06/26
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class PermissionSystemFetch {

    @Qualifier("restTemplate")
    private final RestTemplate restTemplate;

    @Value("${permission-system.domain}")
    private String domain;

    /**
    * 获取权限系统配置
    * @return {@link String}
    */
    @Retryable( value = Exception.class, maxAttemptsExpression = "${retry.maxAttempts}", backoff = @Backoff(delayExpression = "${retry.backoffDelay}"))
    public Set<String> getUserPermission(String ucid) {
        String responseStr = restTemplate.getForObject(StrUtil.format(domain + "/bella/users/{}/roles", ucid), String.class);
        List<UserRoleAndPermissionResponse> responses = JSONUtil.toList(responseStr, UserRoleAndPermissionResponse.class);
        HashSet<String> result = Sets.newHashSet();
        if (CollectionUtils.isEmpty(responses)) {
            return result;
        }

        for (UserRoleAndPermissionResponse response : responses) {
            if (MapUtils.isNotEmpty(response.getPerms())) {
                result.addAll(response.getPerms().keySet());
            }
        }
        return result;
    }

    @Retryable( value = Exception.class, maxAttemptsExpression = "${retry.maxAttempts}", backoff = @Backoff(delayExpression = "${retry.backoffDelay}"))
    public List<RolePermission> listPermission(String ucid) {
        String responseStr = restTemplate.getForObject(StrUtil.format(domain + "/bella/users/{}/roles", ucid), String.class);
        JSONArray jsonArray = JSON.parseArray(responseStr);
        List<RolePermission> list = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            list.add(JSON.parseObject(jsonArray.getString(i),RolePermission.class));
        }
        return list;
    }
}
