package com.ke.chat.tag.configuration;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * 全局RestTemplate配置
 * 确保系统始终有一个可用的passportRestTemplate
 */
@Configuration
public class RestTemplateConfig {

    /**
     * 提供一个全局的passportRestTemplate
     * 这是一个后备bean，如果其他配置没有提供passportRestTemplate，就使用这个
     */
    @Bean(name = "passportRestTemplate")
    public RestTemplate passportRestTemplate() {
        return new RestTemplateBuilder()
                .setConnectTimeout(Duration.ofSeconds(5))
                .setReadTimeout(Duration.ofSeconds(15))
                .build();
    }
} 