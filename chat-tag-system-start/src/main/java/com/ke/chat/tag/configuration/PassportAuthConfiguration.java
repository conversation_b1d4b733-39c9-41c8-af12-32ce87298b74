package com.ke.chat.tag.configuration;

import com.ke.chat.tag.interceptor.PassportAuthInterceptor;
import com.ke.chat.tag.interceptor.PassportWebSocketInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.time.Duration;

/**
 * Passport认证配置类
 * 用于注册PassportAuthInterceptor和配置相关认证逻辑
 */
@Configuration
@Profile("passport") // 只在passport环境下启用
public class PassportAuthConfiguration implements WebMvcConfigurer {

    @Autowired
    private PassportAuthInterceptor passportAuthInterceptor;
    
    @Autowired
    private PassportWebSocketInterceptor passportWebSocketInterceptor;
    
    /**
     * 配置Passport专用的RestTemplate
     */
    @Bean("passportRestTemplate")
    public RestTemplate passportRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        // 设置连接超时
        factory.setConnectTimeout(5000);
        // 设置读取超时
        factory.setReadTimeout(15000);
        
        RestTemplate restTemplate = new RestTemplateBuilder()
                .requestFactory(() -> factory)
                .setConnectTimeout(Duration.ofSeconds(5))
                .setReadTimeout(Duration.ofSeconds(15))
                .build();
        
        // 设置错误处理器，防止HTTP错误状态码导致异常
        restTemplate.setErrorHandler(new DefaultResponseErrorHandler() {
            @Override
            public boolean hasError(org.springframework.http.client.ClientHttpResponse response) throws java.io.IOException {
                // 即使是错误状态码也不抛出异常，让调用方处理
                return false;
            }
        });
        
        return restTemplate;
    }
    
    /**
     * 配置拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加Passport认证拦截器，使用高优先级（-10）确保它比其他拦截器先执行
        registry.addInterceptor(passportAuthInterceptor)
                .addPathPatterns("/api/**") // 拦截所有/api开头的路径
                .excludePathPatterns("/api/v1/health") // 排除健康检查接口
                .excludePathPatterns("/api/v1/user/register") // 排除注册接口
                .excludePathPatterns("/favicon.ico") // 排除图标请求
                .excludePathPatterns("/error") // 排除错误页面
                .order(-10); // 使用高优先级确保先执行
    }
    
    /**
     * 提供HandshakeInterceptor Bean
     */
    @Bean
    public HandshakeInterceptor handshakeInterceptor() {
        return passportWebSocketInterceptor;
    }
} 