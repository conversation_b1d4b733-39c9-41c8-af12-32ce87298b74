package com.ke.chat.tag.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ke.boot.common.exception.BusinessException;
import com.ke.chat.tag.api.dto.application.ApplicationChatRecordDTO;
import com.ke.chat.tag.api.enums.AnswerStatusEnum;
import com.ke.chat.tag.api.enums.MessageTypeEnum;
import com.ke.chat.tag.api.enums.application.ApplicationStatisticsTimeEnum;
import com.ke.chat.tag.api.exception.ChatTagServerException;
import com.ke.chat.tag.dao.entity.ConvQAStatisticDTO;
import com.ke.chat.tag.dao.entity.DataTagReplyRecord;
import com.ke.chat.tag.dao.entity.SaveRecordDTO;
import com.ke.chat.tag.dao.entity.StatisticDTO;
import com.ke.chat.tag.dao.mapper.DataTagReplyRecordMapper;
import com.ke.chat.tag.dao.service.IDataTagLongContentService;
import com.ke.chat.tag.dao.service.IDataTagReplyRecordService;
import com.lianjia.infrastructure.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ke.chat.tag.dao.constant.DataSourceName.MASTER_DATA_SOURCE;

/**
 * <p>
 * 数据标注回答记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
@Service
@Slf4j
public class DataTagReplyRecordServiceImpl extends ServiceImpl<DataTagReplyRecordMapper, DataTagReplyRecord> implements IDataTagReplyRecordService {
    private static final Logger log = LoggerFactory.getLogger(DataTagReplyRecordServiceImpl.class);

    @Autowired
    private IDataTagLongContentService dataTagLongContentService;


    public void saveReply(String content, String questionId, String answerId, boolean success) {
        DataTagReplyRecord record = new DataTagReplyRecord();
        record.setAnswerNo(answerId);
        record.setMessageId(answerId);
        record.setParentId(questionId);
        record.setParentId(questionId);
        record.setReplyStatus(success ? 0 : 1);

        if (content.length() < baseMapper.maxLengthOfReply()) {
            record.setReplyContent(content);
            record.setLongContent(false);
            this.save(record);
        } else {
            record.setReplyContent(content.substring(0, baseMapper.maxLengthOfReply()));
            record.setLongContent(true);
            this.save(record);
            //存储超长内容
            dataTagLongContentService.saveLongContent(answerId, content, MessageTypeEnum.REPLY);
        }
    }

    public void saveReply(String content, String messageId, String questionId, String answerId, boolean success){
        DataTagReplyRecord replyRecord = new DataTagReplyRecord();
        replyRecord.setAnswerNo(answerId);
        replyRecord.setMessageId(answerId);
        replyRecord.setParentId(questionId);
        replyRecord.setReplyStatus(success ? 0 : 1);

        if (content.length() < baseMapper.maxLengthOfReply()) {
            replyRecord.setReplyContent(content);
            replyRecord.setLongContent(false);
            this.save(replyRecord);
        } else {
            replyRecord.setReplyContent(content.substring(0, baseMapper.maxLengthOfReply()));
            replyRecord.setLongContent(true);
            this.save(replyRecord);
            //存储超长内容
            dataTagLongContentService.saveLongContent(answerId, content, MessageTypeEnum.REPLY);
        }
    }

    /**
     * 这个操作是修改数据前的查询，下一步要update，所以查询接口查主库，避免主从延迟导致的异常
     * @param messageId
     * @return
     */
    @Override
    @DS(dataSource = MASTER_DATA_SOURCE, forceMaster = true)
    public List<DataTagReplyRecord> getReplyByMessageId(String messageId) {
        LambdaQueryWrapper<DataTagReplyRecord> queryWrapper = new LambdaQueryWrapper<>();
        List<DataTagReplyRecord> replyRecordList = this.list(queryWrapper.eq(DataTagReplyRecord::getMessageId, messageId));
        if (CollectionUtils.isEmpty(replyRecordList)) {
            log.error("查询返回响应结果为空:{}", messageId);
            throw new ChatTagServerException("查询返回响应结果为空");
        }
        processLongContent(replyRecordList.get(0));
        return replyRecordList;
    }

    @Override
    public List<DataTagReplyRecord> getReplyByMessageIdForDebug(String messageId) {
        LambdaQueryWrapper<DataTagReplyRecord> queryWrapper = new LambdaQueryWrapper<>();
        List<DataTagReplyRecord> replyRecordList = this.list(queryWrapper.eq(DataTagReplyRecord::getMessageId, messageId));
        if (CollectionUtils.isEmpty(replyRecordList)) {
            return Collections.emptyList();
        }
        processLongContent(replyRecordList.get(0));
        return replyRecordList;
    }

    //对于超长字段，传入数据的响应内容字段被更改
    DataTagReplyRecord processLongContent(DataTagReplyRecord dataTagReplyRecord) {
        if (dataTagReplyRecord == null) {
            return null;
        }
        if (dataTagReplyRecord.isLongContent()) {
            dataTagReplyRecord.setReplyContent(dataTagLongContentService.getLongContent(dataTagReplyRecord.getMessageId(), MessageTypeEnum.REPLY));
        }
        return dataTagReplyRecord;
    }

    //注意，对于超长回复，原始回复内容被更改。
    List<DataTagReplyRecord> processLongContent(List<DataTagReplyRecord> dataTagReplyRecords) {
        //使用LinkedHashMap保持传入数据有序性
        LinkedHashMap<String, DataTagReplyRecord> dataTagReplyRecordMap = new LinkedHashMap<>();
        //为兼容旧逻辑，一条回复记录被分段存储在回复表中，此处只过滤第一条记录
        dataTagReplyRecords.forEach(x -> dataTagReplyRecordMap.putIfAbsent(x.getMessageId(), x));
        //处理超长记录
        dataTagReplyRecordMap.values().forEach(this::processLongContent);
        return new ArrayList<>(dataTagReplyRecordMap.values());
    }

    @Override
    public List<DataTagReplyRecord> getReplyByQuestionIds(List<String> questionIds) {
        LambdaQueryWrapper<DataTagReplyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DataTagReplyRecord::getParentId, questionIds).eq(DataTagReplyRecord::getIfDelete, false).orderByAsc(DataTagReplyRecord::getCreateTime);
        List<DataTagReplyRecord> result = list(queryWrapper);
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }
        return processLongContent(result);
    }

    @Override
    public List<DataTagReplyRecord> getUserReply(String userCode, String convId, String answerId) {
        return processLongContent(baseMapper.getUserReply(userCode, convId, answerId));
    }


    @Override
    public List<DataTagReplyRecord> getDataTagReplyRecordsByQuestionIds(List<String> questionIds) {
        if (CollectionUtils.isEmpty(questionIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<DataTagReplyRecord> replyQueryWrapper = new LambdaQueryWrapper<>();
        replyQueryWrapper.in(DataTagReplyRecord::getParentId, questionIds).eq(DataTagReplyRecord::getIfDelete, false);
        return processLongContent(list(replyQueryWrapper));
    }

    @Override
    public List<DataTagReplyRecord> getSuccessDataTagReplyRecordsByQuestionIds(List<String> questionIds, String excludeQuestionId) {
        if (CollectionUtils.isEmpty(questionIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<DataTagReplyRecord> replyQueryWrapper = new LambdaQueryWrapper<>();
        replyQueryWrapper.in(DataTagReplyRecord::getParentId, questionIds).eq(DataTagReplyRecord::getIfDelete, false).eq(DataTagReplyRecord::getReplyStatus, 0).ne(DataTagReplyRecord::getParentId, excludeQuestionId);
        return processLongContent(list(replyQueryWrapper));
    }

    @Override
    public DataTagReplyRecord queryByBizNo(String batchNo, String convId, String answerNo) {
        return processLongContent(baseMapper.queryByTaskBizNo(batchNo, convId, answerNo));
    }

    @Override
    public List<DataTagReplyRecord> selectByParentId(String parentId) {
        return processLongContent(baseMapper.selectByParentId(parentId));
    }

    @Override
    public int updateTagStatusByParentIdAndMessageId(String messageId, String parentId,
                                                     Boolean isTag) {

        return baseMapper.updateTagStatusByParentIdAndMessageId(messageId, parentId, isTag);
    }

    @Override
    public void updateReplyRecord(String messageId, String replyContent, Integer replyStatus, String errorMsg, String specialResponse, String specificError) {
        LambdaUpdateWrapper<DataTagReplyRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DataTagReplyRecord::getMessageId, messageId)
            .set(DataTagReplyRecord::getReplyStatus, replyStatus)
            .set(DataTagReplyRecord::getReplyErrorMsg, errorMsg == null ? "" : errorMsg)
            .set(DataTagReplyRecord::getUpdateTime, LocalDateTime.now())
            .set(DataTagReplyRecord::getSpecialResponse, specialResponse == null ? "" : specialResponse)
            .set(DataTagReplyRecord::getSpecificError, specificError == null ? "" : specificError);

        if (replyContent.length() < baseMapper.maxLengthOfReply()) {
            updateWrapper.eq(DataTagReplyRecord::getMessageId, messageId)
                .set(DataTagReplyRecord::getReplyContent, replyContent)
                .set(DataTagReplyRecord::isLongContent, false);
            this.update(updateWrapper);
        } else {
            updateWrapper.eq(DataTagReplyRecord::getMessageId, messageId)
                .set(DataTagReplyRecord::getReplyContent, replyContent.substring(0, baseMapper.maxLengthOfReply()))
                .set(DataTagReplyRecord::isLongContent, true);
            this.update(updateWrapper);
            //存储超长内容
            dataTagLongContentService.saveLongContent(messageId, replyContent, MessageTypeEnum.REPLY);
        }
    }

    @Override
    public void updateReplyRecordWithFile(String messageId, String replyContent, Integer replyStatus, boolean hasKnowledge) {
        LambdaUpdateWrapper<DataTagReplyRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DataTagReplyRecord::getMessageId, messageId)
            .set(DataTagReplyRecord::getReplyStatus, replyStatus)
            .set(DataTagReplyRecord::getReplyIndex, replyContent.length());

        if (replyContent.length() < baseMapper.maxLengthOfReply()) {
            updateWrapper.eq(DataTagReplyRecord::getMessageId, messageId)
                .set(DataTagReplyRecord::getReplyContent, replyContent)
                .set(DataTagReplyRecord::isLongContent, false);
            this.update(updateWrapper);
        } else {
            updateWrapper.eq(DataTagReplyRecord::getMessageId, messageId)
                .set(DataTagReplyRecord::getReplyContent, replyContent.substring(0, baseMapper.maxLengthOfReply()))
                .set(DataTagReplyRecord::isLongContent, true);
            this.update(updateWrapper);
            //存储超长内容
            dataTagLongContentService.saveLongContent(messageId, replyContent, MessageTypeEnum.REPLY);
        }
    }

    @Override
    public void updateRecordWithFile(String questionId, String model, String messageId, String result, boolean hasKnowledge, long startTime, AnswerStatusEnum answerStatus) {
        List<DataTagReplyRecord> reply = getReplyByMessageId(messageId);
        if (CollectionUtils.isEmpty(reply)) {
            throw new RuntimeException("本次提问出现异常");
        }
        questionId = (questionId == null || questionId.isEmpty()) ? reply.get(0).getParentId() : questionId;
        saveReplyRecordIncludeLongContent(reply.get(0).getId(), model, questionId, messageId, result,
            reply.get(0).getReplyIndex() == 0 ? result.length() : reply.get(0).getReplyIndex(), hasKnowledge, startTime, answerStatus);
    }

    @Override
    public void saveRecordWithFile(SaveRecordDTO saveRecordDTO) {
        DataTagReplyRecord replyRecord = new DataTagReplyRecord();
        replyRecord.setReplyStatus(saveRecordDTO.getAnswerStatus().code);
        Long startTime = saveRecordDTO.getStartTime();
        if (Objects.isNull(startTime)) {
            startTime = System.currentTimeMillis();
        }
        replyRecord.setCostTime((System.currentTimeMillis() - startTime) / 1000);
        replyRecord.setParentId(saveRecordDTO.getQuestionId());
        replyRecord.setMessageId(saveRecordDTO.getRunId());
        replyRecord.setReplyIndex(saveRecordDTO.getResult().length());
        replyRecord.setHasKnowledge(saveRecordDTO.getHasKnowledge());
        replyRecord.setIsTag(false);
        replyRecord.setChannelCode(saveRecordDTO.getModel());
        if (saveRecordDTO.getResult().length() < baseMapper.maxLengthOfReply()) {
            replyRecord.setReplyContent(saveRecordDTO.getResult());
            replyRecord.setLongContent(false);
            this.save(replyRecord);
        } else {
            replyRecord.setReplyContent(saveRecordDTO.getResult().substring(0, baseMapper.maxLengthOfReply()));
            replyRecord.setLongContent(true);
            this.save(replyRecord);
            //存储超长内容
            dataTagLongContentService.saveLongContent(saveRecordDTO.getRunId(), saveRecordDTO.getResult(), MessageTypeEnum.REPLY);
        }
    }

    @Override
    public List<StatisticDTO> selectInteractions(List<String> questionIds, LocalDateTime beginTime, LocalDateTime endTime, ApplicationStatisticsTimeEnum statisticsTimeEnum) {
        if (CollectionUtils.isEmpty(questionIds)) {
            return Collections.emptyList();
        }
        if ("lastOneWeek".equals(statisticsTimeEnum.getName()) || "lastTwoWeek".equals(statisticsTimeEnum.getName())) {
            return baseMapper.selectInteractionsDaily(questionIds, beginTime, endTime);
        } else if ("lastOneMonth".equals(statisticsTimeEnum.getName()) || "lastHalfYear".equals(statisticsTimeEnum.getName())) {
            return baseMapper.selectInteractionsWeekly(questionIds, beginTime, endTime);
        } else {
            return Collections.emptyList();
        }
    }

    public List<ConvQAStatisticDTO> getConvQACount(List<String> convIds) {
        if (convIds == null || convIds.isEmpty()) {
            return Lists.newArrayList();
        }
        return baseMapper.getConvQACount(convIds);
    }

    @Override
    public long getReplySuccessNumsIncludeDeleted(List<String> questionIds) {
        if (CollectionUtils.isEmpty(questionIds)){
            return 0L;
        }
        long totalCount = 0L;
        List<String> questionIdList = questionIds.stream().distinct().collect(Collectors.toList());
        List<List<String>> partitionAll = Lists.partition(questionIdList, 1000);
        for(List<String> partitionParts : partitionAll){
            totalCount = totalCount + baseMapper.getReplySuccessNumsIncludeDeleted(partitionParts);
        }
        return totalCount;
    }

    @Override
    public Page<ApplicationChatRecordDTO> getChatRecord(Page<ApplicationChatRecordDTO> page, List<Long> applicationIds, Integer source,
                                                        Integer isThumbsUp, String applyStatus, String query, String userId, String userName,
                                                        Date lastCreateTime, Long lastId, String tag, String muid,Date timeStart, Date timeEnd,
                                                        List<String> questionEvaluationResults, List<String> replyEvaluationResults) {
        return this.baseMapper.getChatRecord(page, new QueryWrapper<>(), applicationIds, source, isThumbsUp, applyStatus, query, userId,
            userName, lastCreateTime, lastId, tag, muid, timeStart, timeEnd, questionEvaluationResults, replyEvaluationResults);
    }

    @Override
    public void updateReply(String runId, String reply) {
        LambdaUpdateWrapper<DataTagReplyRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DataTagReplyRecord::getMessageId, runId);
        updateWrapper.set(DataTagReplyRecord::getReplyContent, reply);
        update(updateWrapper);
    }

    public void saveReplyRecordIncludeLongContent(Long id, String model, String questionId, String answerId, String result, Integer index, boolean hasKnowledge, long startTime, AnswerStatusEnum answerStatus) {
        DataTagReplyRecord replyRecord = new DataTagReplyRecord();
        replyRecord.setId(id);
        replyRecord.setReplyStatus(answerStatus.code);
        replyRecord.setCostTime((System.currentTimeMillis() - startTime) / 1000);
        replyRecord.setParentId(questionId);
        replyRecord.setMessageId(answerId);
        replyRecord.setReplyIndex(index);
        replyRecord.setMessageId(answerId);
        replyRecord.setHasKnowledge(hasKnowledge);
        replyRecord.setIsTag(false);
        replyRecord.setChannelCode(model);
        if (result.length() < baseMapper.maxLengthOfReply()) {
            replyRecord.setReplyContent(result);
            replyRecord.setLongContent(false);
            this.updateById(replyRecord);
        } else {
            replyRecord.setReplyContent(result.substring(0, baseMapper.maxLengthOfReply()));
            replyRecord.setLongContent(true);
            this.updateById(replyRecord);
            //存储超长内容
            dataTagLongContentService.saveLongContent(answerId, result, MessageTypeEnum.REPLY);
        }
    }

    public void updateReplyRecordIndex(String messageId, Integer index) {
        LambdaQueryWrapper<DataTagReplyRecord> wrapper = new LambdaQueryWrapper<>();
        List<DataTagReplyRecord> reply = list(wrapper.eq(DataTagReplyRecord::getMessageId, messageId));
        if (CollectionUtils.isEmpty(reply)) {
            throw new BusinessException("400", "不存在此回复记录");
        }
        LambdaUpdateWrapper<DataTagReplyRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DataTagReplyRecord::getMessageId, messageId)
            .set(DataTagReplyRecord::getReplyIndex, index);
        update(updateWrapper);
    }

    @Override
    public DataTagReplyRecord getLatestReplyByQuestionId(String messageId) {
        LambdaQueryWrapper<DataTagReplyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataTagReplyRecord::getParentId, messageId).eq(DataTagReplyRecord::getIfDelete, false).orderByDesc(DataTagReplyRecord::getCreateTime);
        List<DataTagReplyRecord> replyRecordList = list(queryWrapper);
        if (CollectionUtils.isEmpty(replyRecordList)) {
            return null;
        } else {
            return processLongContent(replyRecordList.get(0));
        }
    }

    @Override
    public void updateIsTagStatus(String messageId) {
        LambdaUpdateWrapper<DataTagReplyRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DataTagReplyRecord::getMessageId, messageId)
            .set(DataTagReplyRecord::getIsTag, 1);
        this.update(updateWrapper);
    }

    @Override
    public List<String> getAnswerNoWithLongContent() {
        LambdaQueryWrapper<DataTagReplyRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(DataTagReplyRecord::getMessageId)
            .groupBy(DataTagReplyRecord::getMessageId)
            .having("message_id<> '' and count(1) > 1");
        return this.list(lambdaQueryWrapper).stream().map(DataTagReplyRecord::getMessageId).collect(Collectors.toList());
    }

    @Override
    public String getLongContentForOldReply(String messageId) {
        LambdaQueryWrapper<DataTagReplyRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DataTagReplyRecord::getMessageId, messageId)
            .orderByAsc(DataTagReplyRecord::getId);
        List<DataTagReplyRecord> replyRecordList = this.list(lambdaQueryWrapper);
        return replyRecordList.stream().map(DataTagReplyRecord::getReplyContent).collect(Collectors.joining());
    }

    @Override
    public List<DataTagReplyRecord> getReplyByQuestionIds(List<String> questionIds, LocalDateTime beginTime, LocalDateTime endTime) {
        if (CollectionUtils.isEmpty(questionIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<DataTagReplyRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(DataTagReplyRecord::getParentId, questionIds);
        if (beginTime != null) {
            wrapper.ge(DataTagReplyRecord::getCreateTime, beginTime);
        }
        if (endTime != null) {
            wrapper.le(DataTagReplyRecord::getCreateTime, endTime);
        }
        return list(wrapper);
    }

    /**
     * 根据messageId更新replyRecord
     *
     * @return
     */
    public boolean updateByMessageId(DataTagReplyRecord replyRecord) {
        LambdaUpdateWrapper<DataTagReplyRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DataTagReplyRecord::getMessageId, replyRecord.getMessageId());
        return update(replyRecord, updateWrapper);
    }
}
