package com.ke.chat.tag.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.ke.chat.tag.api.enums.knowledge.KnowledgeAuthStatusEnum;
import com.ke.chat.tag.dao.entity.KnowledgeAuth;
import com.ke.chat.tag.dao.mapper.KnowledgeAuthMapper;
import com.ke.chat.tag.dao.service.IKnowledgeAuthService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 知识空间授权关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Slf4j
@Service
public class KnowledgeAuthServiceImpl extends ServiceImpl<KnowledgeAuthMapper, KnowledgeAuth> implements IKnowledgeAuthService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invalidateExistingAuths(String fileId) {
        if (StringUtils.isEmpty(fileId)){
            log.warn("invalidateExistingAuthsByFileId fileId is null");
            return;
        }
        LambdaUpdateWrapper<KnowledgeAuth> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(KnowledgeAuth::getFileId, fileId)
            .set(KnowledgeAuth::getStatus, -1);
        this.update(wrapper);
    }


    @Override
    public List<String> queryFileIds(String authSpaceCode) {

        if (StringUtils.isEmpty(authSpaceCode)){
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<KnowledgeAuth> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(KnowledgeAuth::getFileId);
        queryWrapper.eq(KnowledgeAuth::getAuthSpaceCode, authSpaceCode);
        queryWrapper.eq(KnowledgeAuth::getStatus, KnowledgeAuthStatusEnum.VALID.getCode());

        return Optional.ofNullable(this.baseMapper.selectObjs(queryWrapper))
            .orElse(Lists.newArrayList()) // 处理查询结果为空的情况
            .stream()
            .map(obj -> (String) obj)
            .distinct() // 去重
            .collect(Collectors.toList());
    }

    @Override
    public List<String> queryAuthSpaceCodes(String fileId) {

        if (StringUtils.isEmpty(fileId)){
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<KnowledgeAuth> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(KnowledgeAuth::getAuthSpaceCode);
        queryWrapper.eq(KnowledgeAuth::getFileId, fileId);
        queryWrapper.eq(KnowledgeAuth::getStatus, KnowledgeAuthStatusEnum.VALID.getCode());

        return Optional.ofNullable(this.baseMapper.selectObjs(queryWrapper))
            .orElse(Lists.newArrayList()) // 处理查询结果为空的情况
            .stream()
            .map(obj -> (String) obj)
            .distinct() // 去重
            .collect(Collectors.toList());

    }
}
