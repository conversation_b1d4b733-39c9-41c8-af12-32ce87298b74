package com.ke.chat.tag.dao.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ke.boot.common.exception.BusinessException;
import com.ke.chat.tag.dao.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.redisson.api.RBucket;
import org.redisson.api.RList;
import org.redisson.api.RQueue;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: zongyunbo001
 * @Date: 2023/03/30/18:27
 * @Description:
 */
@Service
@Slf4j
public class RedisServiceImpl implements RedisService {
    private static final Logger log = LoggerFactory.getLogger(RedisServiceImpl.class);

    @Autowired
    RedissonClient redissonClient;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 仅公开的智能体
     */
    private static final String APPLICATION_INTERACTION_KEY = "chat-tag-system:application:interactions";


    /**
     * 主框用户的推荐问题
     */
    private static final String USER_RECOMMEND_QUESTION_PREFIX = "ait:special:weekly_report:";


    /**
     * 入队列
     *
     * @param key
     * @param pushedData
     */
    @Override
    public void pushQueue(String key, String pushedData) {
        RQueue<String> queue = redissonClient.getQueue(key);
        queue.add(pushedData);
    }

    /**
     * 出队列
     *
     * @param key
     * @return
     */
    @Override
    public String popQueue(String key) {
        RQueue<String> queue = redissonClient.getQueue(key);
        return queue.poll();
    }

    @Override
    public int queryQueueSize(String key) {
        RQueue<String> queue = redissonClient.getQueue(key);
        return queue.size();
    }

    @Override
    public void removeApplicationInteraction(Long applicationId) {
        RScoredSortedSet<Long> scoredSortedSet = getInteractionSortedSet();
        scoredSortedSet.remove(applicationId);
    }

    @Override
    public RScoredSortedSet<Long> getInteractionSortedSet() {
        return redissonClient.getScoredSortedSet(APPLICATION_INTERACTION_KEY);
    }

    @Override
    public void increaseInteraction(Long applicationId) {
        RScoredSortedSet<Long> scoredSortedSet = getInteractionSortedSet();
        scoredSortedSet.addScore(applicationId, 1);
    }

    @Override
    public long getInteraction(Long applicationId) {
        RScoredSortedSet<Long> scoredSortedSet = getInteractionSortedSet();
        if (scoredSortedSet == null || scoredSortedSet.isEmpty()) {
            return 0L;
        }
        Double score = scoredSortedSet.getScore(applicationId);
        return score == null ? 0L : score.longValue();
    }

    @Override
    public Map<Long, Long> batchGetInteraction(List<Long> applicationIds) {
        // 如果输入的applicationIds为空，返回空map
        if (applicationIds == null || applicationIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 去重处理，避免重复的applicationId导致map构建失败
        Set<Long> uniqueApplicationIds = new HashSet<>(applicationIds);

        RScoredSortedSet<Long> scoredSortedSet = getInteractionSortedSet();

        // 如果scoredSortedSet为空或者不存在，所有applicationIds对应的值都设为0
        if (scoredSortedSet == null || scoredSortedSet.isEmpty()) {
            return uniqueApplicationIds.stream()
                .collect(Collectors.toMap(Function.identity(), id -> 0L));
        }

        // 正常情况下，获取每个applicationId对应的score，不存在的设为0
        return uniqueApplicationIds.stream()
            .collect(Collectors.toMap(Function.identity(),
                id -> {
                    Double score = scoredSortedSet.getScore(id);
                    return score == null ? 0L : score.longValue();
                }));
    }

    @Override
    public void flushInteraction(Map<Long, Long> applicationInteractionMap) {
        RScoredSortedSet<Long> scoredSortedSet = getInteractionSortedSet();
        scoredSortedSet.delete();
        for (Map.Entry<Long, Long> interactionEntry : applicationInteractionMap.entrySet()) {
            scoredSortedSet.add(interactionEntry.getValue(), interactionEntry.getKey());
        }
    }


    public void saveMapToRedis(String redisKey, Map<String, ?> map) {
        RBucket<String> bucket = redissonClient.getBucket(redisKey);
        try {
            String json = objectMapper.writeValueAsString(map);
            bucket.set(json);
            log.info("Map saved to Redis under key: {}", redisKey);
        } catch (JsonProcessingException e) {
            log.error("Error serializing map for Redis key: {}", redisKey, e);
        }
    }

    public <T> Map<String, T> loadMapFromRedis(String redisKey, Class<T> valueType) {
        RBucket<String> bucket = redissonClient.getBucket(redisKey);
        String json = bucket.get();
        if (StringUtils.isNotBlank(json)) {
            try {
                return objectMapper.readValue(json, objectMapper.getTypeFactory().constructMapType(Map.class, String.class, valueType));
            } catch (IOException e) {
                log.error("Error deserializing map from Redis key: {}", redisKey, e);
            }
        }
        return null;
    }

    /**
     * 在redis中获取用户推荐问题，推荐问题的插入是由绘听团队@建东搭建的workflow创建进去的。
     *
     * @param userId
     * @return
     */
    public List<String> getUserRecommendQuestion(String userId) {
        return this.getStringCodecListFromRedis(USER_RECOMMEND_QUESTION_PREFIX + userId);
    }

    /**
     * 从 Redis 中获取存储为列表的值。
     *
     * @param key Redis 中的键
     * @return 存储在该键下的列表
     */
    public List<String> getStringCodecListFromRedis(String key) {
        if (StringUtils.isBlank(key)) {
            throw new BusinessException("400", "Key cannot be null");
        }
        try {
            RList<String> rList = redissonClient.getList(key, StringCodec.INSTANCE);
            return rList.readAll();
        } catch (Exception e) {
            log.error("Error getting list from Redis", e);
            return Collections.emptyList();
        }
    }

}
