FROM openjdk:8-jdk-alpine

ENV IDC zeus
ENV MODULE chat-tag-system
ENV ENVTYPE test
ENV DEBUGPORT 9008
ENV JMXPORT 9009
ENV MATRIX_CODE_DIR /opt/chat-tag-system/htdocs
ENV MATRIX_APPLOGS_DIR /opt/chat-tag-system/applogs
ENV MATRIX_ACCESSLOGS_DIR /opt/chat-tag-system/logs
ENV MATRIX_LOGS_DIR /opt/chat-tag-system/logs
ENV MATRIX_CACHE_DIR /opt/chat-tag-system/cache
ENV MATRIX_PRIVDATA_DIR /opt/chat-tag-system/privdata

COPY release/ /opt/chat-tag-system/htdocs/
RUN chmod +x /opt/chat-tag-system/htdocs/bin/*.sh

EXPOSE 8080 9008 9009
WORKDIR /opt/chat-tag-system/htdocs
VOLUME ["/opt/chat-tag-system/applogs", "/opt/chat-tag-system/logs", "/opt/chat-tag-system/cache", "/opt/chat-tag-system/privdata"]
<PERSON><PERSON> ["/bin/bash", "-x", "/opt/chat-tag-system/htdocs/bin/run.sh"]
