#!/bin/bash

# 服务状态检查脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

info "=== Chat Tag System 服务状态检查 ==="

# 检查端口监听
info "1. 检查端口8080监听状态..."
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null ; then
    success "✓ 端口8080正在监听"
    PORT_OK=true
else
    error "✗ 端口8080未监听"
    PORT_OK=false
fi

# 检查进程
info "2. 检查Java进程..."
if pgrep -f "chat-tag-system.jar" > /dev/null; then
    success "✓ 发现chat-tag-system进程"
    PROCESS_OK=true
else
    error "✗ 未发现chat-tag-system进程"
    PROCESS_OK=false
fi

# 等待服务启动
if [ "$PORT_OK" = true ]; then
    info "3. 等待服务完全启动..."
    sleep 5
    
    # HTTP健康检查
    info "4. 执行HTTP健康检查..."
    if curl -s -f http://localhost:8080/actuator/health > /dev/null 2>&1; then
        success "✓ 健康检查通过"
        HEALTH_OK=true
        
        # 显示健康检查结果
        info "健康检查响应:"
        curl -s http://localhost:8080/actuator/health | python -m json.tool 2>/dev/null || curl -s http://localhost:8080/actuator/health
    else
        error "✗ 健康检查失败"
        HEALTH_OK=false
    fi
else
    HEALTH_OK=false
fi

# 检查Swagger文档
if [ "$HEALTH_OK" = true ]; then
    info "5. 检查Swagger文档可用性..."
    if curl -s -f http://localhost:8080/swagger-ui.html > /dev/null 2>&1; then
        success "✓ Swagger文档可访问"
        info "Swagger地址: http://localhost:8080/swagger-ui.html"
    else
        warn "⚠ Swagger文档不可访问（可能未配置）"
    fi
fi

# 总结
echo
info "=== 检查结果总结 ==="
if [ "$PORT_OK" = true ] && [ "$PROCESS_OK" = true ] && [ "$HEALTH_OK" = true ]; then
    success "🎉 服务启动成功！"
    info "应用访问地址: http://localhost:8080"
    info "健康检查地址: http://localhost:8080/actuator/health"
    info "可能的API文档: http://localhost:8080/swagger-ui.html"
    exit 0
else
    error "❌ 服务启动失败或未完全启动"
    
    if [ "$PORT_OK" = false ]; then
        error "- 端口8080未监听"
    fi
    
    if [ "$PROCESS_OK" = false ]; then
        error "- 应用进程未运行"
    fi
    
    if [ "$HEALTH_OK" = false ]; then
        error "- 健康检查失败"
    fi
    
    info "请检查应用日志获取详细错误信息"
    exit 1
fi
